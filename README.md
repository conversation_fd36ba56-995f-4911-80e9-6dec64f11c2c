# Simple Webhook API

一个简单的webhook API服务器，用于接收和打印HTTP请求的头部信息和请求体。

## 功能特性

- 支持所有HTTP方法（GET, POST, PUT, DELETE, PATCH等）
- 打印详细的请求信息：
  - 请求时间
  - HTTP方法和URL
  - 客户端IP地址
  - 所有请求头
  - 查询参数
  - 请求体内容
- 支持多种数据格式（JSON, 表单数据, 原始数据等）
- 提供友好的JSON响应

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务器

```bash
# 生产模式
npm start

# 开发模式（自动重启）
npm run dev
```

服务器将在 http://localhost:3000 启动

### 3. 测试webhook

发送请求到 `/webhook` 端点：

```bash
# GET请求
curl http://localhost:3000/webhook

# POST请求（JSON数据）
curl -X POST http://localhost:3000/webhook \
  -H "Content-Type: application/json" \
  -H "X-Custom-Header: test-value" \
  -d '{"message": "Hello webhook!", "data": {"key": "value"}}'

# POST请求（表单数据）
curl -X POST http://localhost:3000/webhook \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "name=test&value=123"

# 带查询参数的请求
curl "http://localhost:3000/webhook?param1=value1&param2=value2"
```

## API端点

- `GET /` - API信息和使用说明
- `GET /health` - 健康检查
- `ALL /webhook` - 主要的webhook端点
- `ALL /webhook/*` - 支持子路径的webhook端点

## 环境变量

- `PORT` - 服务器端口（默认: 3000）

## 示例输出

当收到webhook请求时，控制台会输出类似以下的信息：

```
=== 收到Webhook请求 ===
时间: 2024-01-01T12:00:00.000Z
方法: POST
URL: /webhook?test=123
IP地址: ::1

--- 请求头 ---
host: localhost:3000
user-agent: curl/7.68.0
accept: */*
content-type: application/json
content-length: 45
x-custom-header: test-value

--- 查询参数 ---
{
  "test": "123"
}

--- 请求体 ---
JSON数据: {
  "message": "Hello webhook!",
  "data": {
    "key": "value"
  }
}

========================
```

## 技术栈

- Node.js
- Express.js
- 支持JSON、表单和原始数据解析
