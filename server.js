const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

// 中间件：解析JSON和URL编码的请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 中间件：解析原始请求体（用于其他格式的数据）
app.use(express.raw({ type: '*/*', limit: '10mb' }));

// 打印请求信息的函数
function printRequestInfo(req) {
    console.log('\n=== 收到Webhook请求 ===');
    console.log('时间:', new Date().toISOString());
    console.log('方法:', req.method);
    console.log('URL:', req.url);
    console.log('IP地址:', req.ip || req.connection.remoteAddress);
    
    console.log('\n--- 请求头 ---');
    Object.keys(req.headers).forEach(key => {
        console.log(`${key}: ${req.headers[key]}`);
    });
    
    console.log('\n--- 查询参数 ---');
    if (Object.keys(req.query).length > 0) {
        console.log(JSON.stringify(req.query, null, 2));
    } else {
        console.log('无查询参数');
    }
    
    console.log('\n--- 请求体 ---');
    if (req.body) {
        if (Buffer.isBuffer(req.body)) {
            console.log('原始数据 (Buffer):', req.body.toString());
        } else if (typeof req.body === 'object') {
            console.log('JSON数据:', JSON.stringify(req.body, null, 2));
        } else {
            console.log('数据:', req.body);
        }
    } else {
        console.log('无请求体');
    }
    
    console.log('\n========================\n');
}

// 处理所有HTTP方法的webhook端点
app.all('/webhook', (req, res) => {
    printRequestInfo(req);
    
    // 返回成功响应
    res.status(200).json({
        success: true,
        message: 'Webhook received successfully',
        timestamp: new Date().toISOString(),
        method: req.method,
        headers_count: Object.keys(req.headers).length,
        has_body: !!req.body
    });
});

// 处理所有其他路径的webhook请求
app.all('/webhook/*', (req, res) => {
    printRequestInfo(req);
    
    res.status(200).json({
        success: true,
        message: 'Webhook received successfully',
        timestamp: new Date().toISOString(),
        method: req.method,
        path: req.path,
        headers_count: Object.keys(req.headers).length,
        has_body: !!req.body
    });
});

// 根路径 - 显示API信息
app.get('/', (req, res) => {
    res.json({
        message: 'Simple Webhook API',
        endpoints: {
            webhook: '/webhook',
            webhook_with_path: '/webhook/*'
        },
        supported_methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        description: 'Send requests to /webhook to see headers and body printed in console'
    });
});

// 健康检查端点
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 Webhook API服务器已启动`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🌐 本地访问: http://localhost:${PORT}`);
    console.log(`📝 Webhook端点: http://localhost:${PORT}/webhook`);
    console.log(`💡 发送请求到 /webhook 来查看请求头和请求体信息`);
    console.log(`\n等待webhook请求...\n`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('\n收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});
